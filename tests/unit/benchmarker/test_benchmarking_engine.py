import subprocess
import pytest
from unittest.mock import MagicMock, patch, ANY
from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import ModelProfile, SystemProfile, BenchmarkResult, OptimalConfiguration

@pytest.fixture
def mock_model_profile():
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=30,
        quantization_type="Q4_0"
    )

@pytest.fixture
def mock_system_profile():
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[],
        numa_detected=False,
        blas_backend="BLIS"
    )

@pytest.fixture
def benchmarking_engine():
    return BenchmarkingEngine()

def test_run_benchmark_calls_analyzer_and_gpu_offload(benchmarking_engine, mock_model_profile, mock_system_profile):
    with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile) as mock_get_system_profile, \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile) as mock_get_model_profile, \
         patch.object(benchmarking_engine, '_run_gpu_offload_benchmark') as mock_gpu_offload, \
         patch.object(benchmarking_engine, '_run_throughput_benchmark') as mock_throughput_benchmark:
        
        mock_gpu_offload.return_value = (BenchmarkResult(
            n_gpu_layers=10,
            prompt_speed_tps=50.0,
            generation_speed_tps=20.0,
            batch_size=None,
            parallel_level=None
        ), [])
        mock_throughput_benchmark.return_value = BenchmarkResult(
            n_gpu_layers=10,
            prompt_speed_tps=0.0,
            generation_speed_tps=30.0, # Better throughput
            batch_size=8,
            parallel_level=4
        )

        result = benchmarking_engine.run_benchmark("/path/to/model.gguf", 512, "default", num_runs=1, progress_callback=MagicMock())

        mock_get_system_profile.assert_called_once()
        mock_get_model_profile.assert_called_once_with("/path/to/model.gguf")
        mock_gpu_offload.assert_called_once_with(mock_model_profile, 512, 1, ANY, None)
        mock_throughput_benchmark.assert_not_called() # Should not be called for "default" use case
        assert isinstance(result, OptimalConfiguration)
        assert result.best_benchmark_result.n_gpu_layers == 10

    def test_run_benchmark_calls_throughput_benchmark_for_multi_user(self, benchmarking_engine, mock_model_profile, mock_system_profile):
        with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
             patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile), \
             patch.object(benchmarking_engine, '_run_gpu_offload_benchmark') as mock_gpu_offload, \
             patch.object(benchmarking_engine, '_run_throughput_benchmark') as mock_throughput_benchmark:

            mock_gpu_offload.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=50.0,
                generation_speed_tps=20.0,
                batch_size=None,
                parallel_level=None
            )
            mock_throughput_benchmark.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=0.0,
                generation_speed_tps=30.0, # Better throughput
                batch_size=8,
                parallel_level=4
            )

            result = benchmarking_engine.run_benchmark("/path/to/model.gguf", 512, "multi-user-server", num_runs=1, progress_callback=MagicMock())

            mock_gpu_offload.assert_called_once_with(mock_model_profile, 512, 1, ANY, None)
            mock_throughput_benchmark.assert_called_once_with(mock_model_profile, 512, 10, ANY)
            assert isinstance(result, OptimalConfiguration)
            assert result.best_benchmark_result.generation_speed_tps == 30.0 # Should pick the better throughput result
            assert result.best_benchmark_result.batch_size == 8
            assert result.best_benchmark_result.parallel_level == 4

    def test_run_benchmark_progress_callback_called(self, benchmarking_engine, mock_model_profile, mock_system_profile):
        with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
             patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile), \
             patch.object(benchmarking_engine, '_run_gpu_offload_benchmark') as mock_gpu_offload, \
             patch.object(benchmarking_engine, '_run_throughput_benchmark') as mock_throughput_benchmark:

            mock_gpu_offload.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=50.0,
                generation_speed_tps=20.0,
                batch_size=None,
                parallel_level=None
            )
            mock_throughput_benchmark.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=0.0,
                generation_speed_tps=30.0,
                batch_size=8,
                parallel_level=4
            )

            mock_progress_callback = MagicMock()
            benchmarking_engine.run_benchmark("/path/to/model.gguf", 512, "multi-user-server", num_runs=3, progress_callback=mock_progress_callback)

            # Check that the progress callback was called for both phases and with run info
            # Phase 1: GPU Offload Benchmark (binary search, approx 5-6 steps * 3 runs)
            # Phase 2: Throughput Benchmark (parallel levels * 3 runs + batch sizes * 3 runs)
            # Total calls will be significant, so we'll check for specific calls rather than total count.
            mock_progress_callback.assert_any_call("Phase 1/2 GPU Offload", "Starting...", 0, 1)
            mock_progress_callback.assert_any_call("Phase 2/2 Throughput", "Starting...", 0, 1)
            # Check for a specific run update call in Phase 1
            mock_progress_callback.assert_any_call(ANY, ANY, ANY, ANY, ANY, 1, 3)
            # Check for a specific run update call in Phase 2
            mock_progress_callback.assert_any_call(ANY, ANY, ANY, ANY, ANY, 1, 3)

class TestGpuOffloadBenchmark:
   def test_gpu_offload_benchmark_binary_search(self, benchmarking_engine, mock_model_profile):
        """Test the GPU offload benchmark with binary search to find the VRAM limit."""
        # Simulate success for layers 0-15, and failure for 16 and above
        def mock_check_layers(model_profile, ctx_size, n_gpu_layers):
            if n_gpu_layers <= 15:
                # Simulate varying performance to test if the best is found
                performance = 20.0 + (n_gpu_layers * 0.5)
                return BenchmarkResult(
                    n_gpu_layers=n_gpu_layers,
                    prompt_speed_tps=100.0,
                    generation_speed_tps=performance,
                    batch_size=None,
                    parallel_level=None
                )
            else:
                # Simulate OOM crash
                return None

        with patch.object(benchmarking_engine, '_check_n_gpu_layers', side_effect=mock_check_layers) as mock_check:
            result, _ = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 512, num_runs=3)

            # The binary search should find that the best performing layer count is 15
            assert result.n_gpu_layers == 15
            # The generation speed should correspond to the performance at 15 layers
            assert result.generation_speed_tps == 20.0 + (15 * 0.5)

            # Check that the binary search was efficient and didn't test all layers
            # A binary search on 0-30 should take around 5-6 steps.
            assert mock_check.call_count < 10
            # It should have tested the highest successful layer (15) and the one that failed (16)
            mock_check.assert_any_call(ANY, 512, 15)
            mock_check.assert_any_call(ANY, 512, 16)

   def test_gpu_offload_benchmark_no_llama_bench_executable(self, benchmarking_engine, mock_model_profile):
       with patch.object(benchmarking_engine, '_check_n_gpu_layers', return_value=None) as mock_check:
           result, _ = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 512, num_runs=3)
           assert result.n_gpu_layers == 0
           assert result.prompt_speed_tps == 0.0
           assert result.generation_speed_tps == 0.0
           assert mock_check.call_count > 0 # Binary search will still make calls before failing

   def test_gpu_offload_benchmark_statistical_analysis(self, benchmarking_engine, mock_model_profile):
        # Simulate multiple runs with varying performance for a specific n_gpu_layers (e.g., 15)
        # The binary search will converge on this n_gpu_layers if it's the highest successful one.
        mock_individual_results = [
            {"prompt_speed_tps": 100.0, "generation_speed_tps": 20.0},
            {"prompt_speed_tps": 110.0, "generation_speed_tps": 22.0},
            {"prompt_speed_tps": 105.0, "generation_speed_tps": 21.0},
        ]
        
        # Expected means
        expected_prompt_mean = sum([r["prompt_speed_tps"] for r in mock_individual_results]) / len(mock_individual_results)
        expected_generation_mean = sum([r["generation_speed_tps"] for r in mock_individual_results]) / len(mock_individual_results)

        # Expected stddev (sample standard deviation)
        expected_prompt_std = (sum([(r["prompt_speed_tps"] - expected_prompt_mean)**2 for r in mock_individual_results]) / (len(mock_individual_results) - 1))**0.5
        expected_generation_std = (sum([(r["generation_speed_tps"] - expected_generation_mean)**2 for r in mock_individual_results]) / (len(mock_individual_results) - 1))**0.5

        # Mock _check_n_gpu_layers to simulate successful runs for n_gpu_layers <= 15
        # and then return None for n_gpu_layers > 15 (simulating OOM)
        call_sequence = []
        def mock_check_layers_for_stats(model_profile, ctx_size, n_gpu_layers):
            if n_gpu_layers <= 15:
                # Cycle through mock_individual_results for each run
                run_data = mock_individual_results[len(call_sequence) % len(mock_individual_results)]
                call_sequence.append(n_gpu_layers) # Track calls
                return BenchmarkResult(
                    n_gpu_layers=n_gpu_layers,
                    prompt_speed_tps=run_data["prompt_speed_tps"],
                    generation_speed_tps=run_data["generation_speed_tps"],
                    batch_size=None,
                    parallel_level=None
                )
            else:
                call_sequence.append(n_gpu_layers) # Track calls
                return None

        with patch.object(benchmarking_engine, '_check_n_gpu_layers', side_effect=mock_check_layers_for_stats) as mock_check:
            result = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 512, num_runs=len(mock_individual_results))

            assert result.n_gpu_layers == 15 # Should find 15 as the optimal
            assert result.prompt_speed_tps_mean == pytest.approx(expected_prompt_mean)
            assert result.prompt_speed_tps_std == pytest.approx(expected_prompt_std)
            assert result.generation_speed_tps_mean == pytest.approx(expected_generation_mean)
            assert result.generation_speed_tps_std == pytest.approx(expected_generation_std)
            assert len(result.individual_results) == len(mock_individual_results)
            # The number of calls to _check_n_gpu_layers will depend on the binary search path
            # and the number of runs for the highest successful layer.
            # For 30 layers, binary search takes about 5 steps. Each step has 3 runs.
            # So, it should be around 5*3 = 15 calls, but could be less if it hits OOM early.
            assert mock_check.call_count > 0 # Ensure it was called at least once
            assert mock_check.call_count <= (mock_model_profile.layer_count.bit_length() + 1) * len(mock_individual_results) # Upper bound

class TestThroughputBenchmark:
   @pytest.mark.asyncio
   @pytest.mark.parametrize("mock_throughput, expected_batch_size, expected_parallel_level", [
       (15.0, 4, 2),
       (25.0, 8, 4),
       (35.0, 16, 8),
   ])
   async def test_run_throughput_benchmark_success(self, benchmarking_engine, mock_model_profile, mock_throughput, expected_batch_size, expected_parallel_level):
       # Mock subprocess.Popen for llama-server
       mock_server_process = MagicMock()
       mock_server_process.stdout.readline.return_value = "HTTP server listening" # Simulate server startup message
       mock_server_process.poll.return_value = None # Server stays running
       mock_server_process.stderr.read.return_value = ""

       # Mock asyncio.run to control the simulated client throughput
       # The implementation has two stages:
       # Stage 1: parallel_levels = [1, 2, 4, 8, 16, 32] (6 calls)
       # Stage 2: batch_sizes = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512] (10 calls)
       # Each stage now has num_runs iterations.
       num_runs = 3

       # Create mock values where the expected parallel level has the highest throughput
       stage1_values = []
       parallel_levels = [1, 2, 4, 8, 16, 32]
       for parallel_level in parallel_levels:
           base_throughput = 60.0 if parallel_level == expected_parallel_level else 10.0
           for run in range(num_runs):
               stage1_values.append(base_throughput + run * 0.1)

       # Create mock values for Stage 2 where the expected batch size has the highest throughput
       stage2_values = []
       batch_sizes = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512]
       for batch_size in batch_sizes:
           base_throughput = 200.0 if batch_size == expected_batch_size else 10.0
           for run in range(num_runs):
               stage2_values.append(base_throughput + run * 0.1)

       mock_values = stage1_values + stage2_values
       call_idx = 0
       def mock_asyncio_run(coro):
           nonlocal call_idx
           if call_idx < len(mock_values):
               value = mock_values[call_idx]
               call_idx += 1
               return value
           raise IndexError("mock_asyncio_run side_effect exhausted")

       with patch('subprocess.Popen', return_value=mock_server_process) as mock_popen, \
            patch('asyncio.run', side_effect=mock_asyncio_run) as mock_asyncio_run_patch, \
            patch('time.sleep', return_value=None) as mock_sleep: # Mock sleep to speed up tests
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, num_runs=num_runs, progress_callback=mock_progress_callback)
           
           # The final result should have the optimal batch size with the highest throughput (200.0)
           expected_individual_results = [200.0, 200.1, 200.2]
           expected_mean = sum(expected_individual_results) / num_runs
           expected_std = (sum([(x - expected_mean)**2 for x in expected_individual_results]) / (num_runs - 1))**0.5 if num_runs > 1 else 0.0

           assert result.generation_speed_tps == pytest.approx(expected_mean)
           assert result.generation_speed_tps_mean == pytest.approx(expected_mean)
           assert result.generation_speed_tps_std == pytest.approx(expected_std)
           assert result.batch_size == expected_batch_size
           assert result.parallel_level == expected_parallel_level
           assert len(result.individual_results) == num_runs
           
           # Verify server process was started and terminated
           mock_popen.assert_called()
           mock_server_process.terminate.assert_called()
           mock_server_process.wait.assert_called()

           # Verify progress callback was called
           assert mock_progress_callback.call_count > 0
           mock_progress_callback.assert_any_call(ANY, ANY, ANY, ANY, ANY, num_runs, num_runs)

   @pytest.mark.asyncio
   async def test_run_throughput_benchmark_server_startup_failure(self, benchmarking_engine, mock_model_profile):
       # Simulate server failing to start
       mock_server_process = MagicMock()
       mock_server_process.stdout.readline.side_effect = ["", ""] # No startup message
       mock_server_process.poll.return_value = 1 # Server exits immediately
       mock_server_process.stderr.read.return_value = "Error: Port already in use"

       with patch('subprocess.Popen', return_value=mock_server_process) as mock_popen, \
            patch('asyncio.run', return_value=0.0) as mock_asyncio_run, \
            patch('time.sleep', return_value=None) as mock_sleep:
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, num_runs=3, progress_callback=mock_progress_callback)
           
           assert result.generation_speed_tps == 0.0 # No successful runs
           mock_popen.assert_called()
           mock_asyncio_run.assert_not_called() # Client simulation should not run
           mock_server_process.terminate.assert_not_called() # Server didn't start, no need to terminate

           # Progress callback should be called for each step attempted (only once per step since server fails)
           # The algorithm tries a few combinations before giving up
           assert mock_progress_callback.call_count >= 3  # At least a few attempts

   @pytest.mark.asyncio
   async def test_run_throughput_benchmark_no_llama_server_executable(self, benchmarking_engine, mock_model_profile):
       with patch('subprocess.Popen', side_effect=FileNotFoundError) as mock_popen, \
            patch('asyncio.run', return_value=0.0) as mock_asyncio_run, \
            patch('time.sleep', return_value=None) as mock_sleep:
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, num_runs=3, progress_callback=mock_progress_callback)
           
           assert result.generation_speed_tps == 0.0
           assert mock_popen.call_count >= 1  # Should attempt to run at least once and fail
           mock_asyncio_run.assert_not_called()
           # Progress callback is called for each attempt, even if executable not found
           assert mock_progress_callback.call_count >= 1

   @pytest.mark.asyncio
   async def test_run_throughput_benchmark_client_simulation_failure(self, benchmarking_engine, mock_model_profile):
       mock_server_process = MagicMock()
       mock_server_process.stdout.readline.side_effect = ["HTTP server listening", ""]
       mock_server_process.poll.return_value = None
       mock_server_process.stderr.read.return_value = ""

       with patch('subprocess.Popen', return_value=mock_server_process) as mock_popen, \
            patch('asyncio.run', side_effect=Exception("Client simulation error")) as mock_asyncio_run, \
            patch('time.sleep', return_value=None) as mock_sleep:
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, num_runs=3, progress_callback=mock_progress_callback)
           
           assert result.generation_speed_tps == 0.0
           mock_popen.assert_called()
           assert mock_asyncio_run.call_count >= 1  # Should attempt client simulation at least once and fail
           mock_server_process.terminate.assert_called() # Server should still be terminated

           # Progress callback should be called for each step attempted
           assert mock_progress_callback.call_count >= 1  # At least one attempt

