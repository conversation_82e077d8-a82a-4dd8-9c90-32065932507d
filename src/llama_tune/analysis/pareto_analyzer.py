from typing import List, Dict, Any

def is_dominated(p: Dict[str, Any], q: Dict[str, Any], objectives: List[str]) -> bool:
    """Checks if point p is dominated by point q."""
    # At least as good in all objectives
    # and strictly better in at least one objective
    return all(p[obj] <= q[obj] for obj in objectives) and any(p[obj] < q[obj] for obj in objectives)

def get_pareto_front(points: List[Dict[str, Any]], objectives: List[str]) -> List[Dict[str, Any]]:
    """Calculates the Pareto front for a given set of points and objectives."""
    pareto_front = []
    for p in points:
        is_p_dominated = False
        for q in points:
            if p == q:
                continue
            if is_dominated(p, q, objectives):
                is_p_dominated = True
                break
        if not is_p_dominated:
            pareto_front.append(p)
    return pareto_front
